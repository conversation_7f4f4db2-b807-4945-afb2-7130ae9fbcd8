/* ===========
 * 作者: Zhu<PERSON><PERSON><PERSON>
 * 创建时间: 2025/07/01
 * 非作者最新修改时间：  
 * =========== */
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
// using System.Security.Policy;
using System.Threading.Tasks;
using Downloader;
using MHLab.Patch.Core.Utilities;
using Newtonsoft.Json;
using VIC.Launcher.FSS;

namespace VIC.Launcher
{
    internal class FileCacheManager
    {
        private const string INDEX_FILE_NAME = "index.json";

        private readonly string cacheDirectory;
        private readonly string baseUrl;
        private Dictionary<string, CachedFileInfo> remoteIndex;
        private Dictionary<string, CachedFileInfo> localIndex;
        private readonly string localIndexPath;

        public FileCacheManager(string baseUrl, string cacheDirectory)
        {
            this.cacheDirectory = cacheDirectory;
            this.baseUrl = baseUrl;
            localIndexPath = Path.Combine(cacheDirectory, INDEX_FILE_NAME);

            // 确保缓存目录存在
            Directory.CreateDirectory(cacheDirectory);

            // 初始化本地索引
            localIndex = LoadLocalIndex();
        }

        private Dictionary<string, CachedFileInfo> LoadLocalIndex()
        {
            if (File.Exists(localIndexPath))
            {
                try
                {
                    string json = File.ReadAllText(localIndexPath);
                    return JsonConvert.DeserializeObject<Dictionary<string, CachedFileInfo>>(json);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载本地索引失败: {ex.Message}");
                }
            }

            return new Dictionary<string, CachedFileInfo>();
        }

        private void SaveLocalIndex()
        {
            try
            {
                string json = JsonConvert.SerializeObject(localIndex);
                File.WriteAllText(localIndexPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存本地索引失败: {ex.Message}");
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                // 尝试从远程获取索引
                // 🔥 使用防盗链处理索引文件URL
                string secureIndexUrl = VICAntiHotlinkHelper.ProcessUrl($"{baseUrl}/{INDEX_FILE_NAME}");
                IDownload download = DownloadBuilder.New()
                    .WithUrl(secureIndexUrl)
                    .Build();

                var memoryStream = await download.StartAsync();
                if (download.Status != DownloadStatus.Completed)
                {
                    throw new Exception("下载索引文件失败");
                }

                using var streamReader = new StreamReader(memoryStream);
                string json = await streamReader.ReadToEndAsync();
                remoteIndex = JsonConvert.DeserializeObject<Dictionary<string, CachedFileInfo>>(json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取远程索引失败，将使用本地索引: {ex.Message}");
                remoteIndex = new Dictionary<string, CachedFileInfo>(localIndex);
            }
        }

        // 1. 获取文件列表
        public List<string> GetFileList()
        {
            return new List<string>(remoteIndex.Keys);
        }

        // 2. 获取指定目录下全部文件
        public List<string> GetFilesInDirectory(string directory)
        {
            List<string> result = new List<string>();

            foreach (string path in remoteIndex.Keys)
            {
                if (path.StartsWith(directory))
                {
                    result.Add(path);
                }
            }

            return result;
        }

        // 3. 获取指定文件
        public async Task<string> GetFileAsync(string filePath, Action<DownloadReport> onGetFileProgress = null, bool mustSync = false)
        {
            if (!remoteIndex.ContainsKey(filePath))
            {
                throw new FileNotFoundException($"指定文件 {filePath} 未在索引中找到");
            }

            CachedFileInfo remoteFileInfo = remoteIndex[filePath];
            string localFilePath = Path.Combine(cacheDirectory, filePath);
            bool needsDownload = true;

            long fileSize = remoteFileInfo.Size;

            // 检查本地文件是否存在且是最新版本
            if (localIndex.TryGetValue(filePath, out CachedFileInfo localFileInfo))
            {
                if (File.Exists(localFilePath) &&
                    localFileInfo.LastModified == remoteFileInfo.LastModified &&
                    localFileInfo.Size == remoteFileInfo.Size &&
                    localFileInfo.Hash == remoteFileInfo.Hash)
                {
                    needsDownload = false;
                }
            }

            DownloadReport report = new()
            {
                DownloadedSize = 0,
                TotalSize = fileSize,
                DownloadSpeed = "0 B/s"
            };

            if (needsDownload)
            {
                try
                {
                    // 确保目录存在
                    Directory.CreateDirectory(Path.GetDirectoryName(localFilePath));

                    // 下载文件
                    // 🔥 使用防盗链处理文件URL
                    string secureFileUrl = VICAntiHotlinkHelper.ProcessUrl($"{baseUrl}/{filePath}");
                    IDownload download = DownloadBuilder.New()
                        .WithUrl(secureFileUrl)
                        .WithFileLocation(localFilePath)
                        .Build();

                    download.DownloadProgressChanged += (s, e) =>
                    {
                        report.DownloadedSize = e.ReceivedBytesSize;
                        report.DeltaSize = e.ProgressedByteSize;
                        report.DownloadSpeed = FormatUtility.FormatSizeDecimal(e.BytesPerSecondSpeed, 2) + "/s";
                        onGetFileProgress?.Invoke(report);
                    };

                    await download.StartAsync();
                    if (download.Status != DownloadStatus.Completed)
                    {
                        throw new Exception($"下载失败");
                    }

                    // 更新本地索引
                    localIndex[filePath] = remoteFileInfo;
                    SaveLocalIndex();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"下载文件 {filePath} 失败: {ex.Message}");

                    // 如果下载失败且强制同步或本地没有缓存，则抛出异常
                    if (mustSync || !File.Exists(localFilePath))
                    {
                        throw;
                    }
                }
            }
            else
            {
                // 如果本地文件存在且是最新版本，则直接返回
                report.DownloadedSize = fileSize;
                report.TotalSize = fileSize;
                report.DeltaSize = fileSize;
                report.DownloadSpeed = FormatUtility.FormatSizeDecimal(fileSize, 2) + "/s";
                onGetFileProgress?.Invoke(report);
            }

            // 返回本地文件路径
            return localFilePath;
        }

        // 4. 下载指定目录的全部文件并返回文件路径列表
        public async Task<List<string>> DownloadDirectoryAsync(string directory, Action<DownloadReport> onGetFileProgress = null, bool mustSync = false)
        {
            List<string> filesToDownload = GetFilesInDirectory(directory);
            List<string> downloadedFilePaths = new List<string>();

            long totalSize = 0;
            foreach (string filePath in filesToDownload)
            {
                if (remoteIndex.TryGetValue(filePath, out CachedFileInfo fileInfo))
                {
                    totalSize += fileInfo.Size;
                }
            }

            DownloadReport overallReport = new()
            {
                DownloadedSize = 0,
                TotalSize = totalSize,
                DownloadSpeed = "0 B/s"
            };

            void OnGetFileProgress(DownloadReport report)
            {
                overallReport.DownloadedSize += report.DeltaSize;
                overallReport.DownloadSpeed = report.DownloadSpeed;

                onGetFileProgress?.Invoke(overallReport);
            }

            foreach (string filePath in filesToDownload)
            {
                try
                {
                    string localPath = await GetFileAsync(filePath, OnGetFileProgress, mustSync);
                    downloadedFilePaths.Add(localPath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"下载文件 {filePath} 失败: {ex.Message}");
                    // 继续下载其他文件
                    if (mustSync)
                    {
                        throw;
                    }
                }
            }

            overallReport.DownloadedSize = totalSize;
            onGetFileProgress?.Invoke(overallReport);

            return downloadedFilePaths;
        }
    }

    public class CachedFileInfo
    {
        public long Size { get; set; }
        public string LastModified { get; set; }
        public string Hash { get; set; }
    }
}