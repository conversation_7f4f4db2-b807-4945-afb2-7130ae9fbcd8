/* ===========
 * 作者: Frenqy
 * 首次创建时间: 
 * 最新修改时间: 2025/03/21
 * ===========
 */
#if UNITY_EDITOR || UNITY_STANDALONE_WIN
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using UnityEngine;

namespace VIC.Utils
{
    /// <summary>
    /// 注册表辅助类, 用于存储和读取注册表中的信息
    /// </summary>
    public class RegistryHelper : MonoBehaviour
    {
#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        // Windows Registry API constants
        private const int HKEY_CURRENT_USER = unchecked((int)0x80000001);
        private const int KEY_READ = 0x20019;
        private const int KEY_WRITE = 0x20006;
        private const int KEY_ALL_ACCESS = 0xF003F;
        private const int REG_SZ = 1;
        private const int ERROR_SUCCESS = 0;
        private const int ERROR_FILE_NOT_FOUND = 2;

        // Windows Registry API functions
        [DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
        private static extern int RegOpenKeyEx(int hKey, string subKey, int ulOptions, int samDesired, out int hkResult);

        [DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
        private static extern int RegCreateKeyEx(int hKey, string subKey, int reserved, string lpClass, int dwOptions, int samDesired, int lpSecurityAttributes, out int phkResult, out int lpdwDisposition);

        [DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
        private static extern int RegSetValueEx(int hKey, string lpValueName, int reserved, int dwType, string lpData, int cbData);

        [DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
        private static extern int RegQueryValueEx(int hKey, string lpValueName, int lpReserved, out int lpType, System.Text.StringBuilder lpData, ref int lpcbData);

        [DllImport("advapi32.dll")]
        private static extern int RegCloseKey(int hKey);

        [DllImport("advapi32.dll", CharSet = CharSet.Unicode)]
        private static extern int RegEnumValue(int hkey, int index, System.Text.StringBuilder lpValueName, ref int lpcchValueName, int lpReserved, out int lpType, System.Text.StringBuilder lpData, ref int lpcbData);
#endif
        public static RegistryHelper Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<RegistryHelper>();
                    if (instance == null)
                    {
                        instance = new GameObject(nameof(RegistryHelper)).AddComponent<RegistryHelper>();
                    }
                }
                return instance;
            }
        }
        private static RegistryHelper instance;

        private const string COMPANY_NAME = "VICFUN";
        private const string STORAGE_KEY = "Storage";
        private const string LAUNCHER_KEY = "Launcher";

        public bool IsLauncherRegistered => isLauncherRegistered;
        public string LauncherPath => launcherPath;

#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        private int userKey;
        private int companyKey;
        private int storageKey;
#endif

        private bool isInitialized = false;

        private bool isLauncherRegistered = false;
        private string launcherPath = string.Empty;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
#pragma warning disable IDE0051
        private static void AutoInitRegistry()
#pragma warning restore IDE0051
        {
            Instance.InitRegistry();
        }

        private void Awake()
        {
            if (instance != null && instance != this)
            {
                Destroy(gameObject);
                return;
            }
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitRegistry();
        }

        private void OnDestroy()
        {
#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
            if (userKey != 0) RegCloseKey(userKey);
            if (companyKey != 0) RegCloseKey(companyKey);
            if (storageKey != 0) RegCloseKey(storageKey);
#endif
        }

        private void InitRegistry()
        {
            if (isInitialized)
            {
                return;
            }
            isInitialized = true;

#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
            // Open HKEY_CURRENT_USER\Software
            if (RegOpenKeyEx(HKEY_CURRENT_USER, "Software", 0, KEY_ALL_ACCESS, out userKey) == ERROR_SUCCESS)
            {
                companyKey = GetOrCreateSubKey(userKey, COMPANY_NAME);
                storageKey = GetOrCreateSubKey(companyKey, STORAGE_KEY);

                (isLauncherRegistered, launcherPath) = GetStringValue(storageKey, LAUNCHER_KEY);

                // set the current software path
                var (isCurrentSoftwareRegistered, currentSoftwarePath) = GetStringValue(storageKey, Application.productName);
                string executablePath = Path.GetDirectoryName(Application.dataPath);
                if (!isCurrentSoftwareRegistered || currentSoftwarePath != executablePath)
                {
                    SetStringValue(storageKey, Application.productName, executablePath);
                }
            }
#endif
        }

        public Dictionary<string, string> GetSoftwarePaths()
        {
            Dictionary<string, string> softwarePaths = new();
#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
            if (storageKey != 0)
            {
                int index = 0;
                var valueName = new System.Text.StringBuilder(256);
                var valueData = new System.Text.StringBuilder(1024);

                while (true)
                {
                    int nameSize = valueName.Capacity;
                    int dataSize = valueData.Capacity;

                    int result = RegEnumValue(storageKey, index, valueName, ref nameSize, 0, out int type, valueData, ref dataSize);
                    if (result != ERROR_SUCCESS) break;

                    string name = valueName.ToString();
                    if (name != LAUNCHER_KEY)
                    {
                        string data = valueData.ToString();
                        softwarePaths.Add(name, data);
                    }

                    index++;
                    valueName.Clear();
                    valueData.Clear();
                }
            }
#endif
            return softwarePaths;
        }

#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
        private static int GetOrCreateSubKey(int mainKey, string subKeyName)
        {
            int subKey;
            // Try to open existing key
            if (RegOpenKeyEx(mainKey, subKeyName, 0, KEY_ALL_ACCESS, out subKey) == ERROR_SUCCESS)
            {
                return subKey;
            }

            // Create new key if it doesn't exist
            if (RegCreateKeyEx(mainKey, subKeyName, 0, null, 0, KEY_ALL_ACCESS, 0, out subKey, out int disposition) == ERROR_SUCCESS)
            {
                return subKey;
            }

            return 0; // Failed to create/open key
        }

        private static void SetStringValue(int key, string name, string value)
        {
            if (key != 0)
            {
                RegSetValueEx(key, name, 0, REG_SZ, value, (value.Length + 1) * 2);
            }
        }

        private static (bool, string) GetStringValue(int key, string name)
        {
            if (key == 0) return (false, string.Empty);

            var data = new System.Text.StringBuilder(1024);
            int dataSize = data.Capacity * 2;

            if (RegQueryValueEx(key, name, 0, out int type, data, ref dataSize) == ERROR_SUCCESS)
            {
                return (true, data.ToString());
            }

            return (false, string.Empty);
        }
#endif
    }
}
#endif
